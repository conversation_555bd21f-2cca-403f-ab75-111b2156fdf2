<?php

namespace App\Models;

use App\Enums\PartnerTypes;
use App\Models\Order\Order;
use App\Enums\DeliveryTerms;
use App\Models\Project\Project;
use App\Enums\CommercialCategories;
use App\Enums\ClientPartnerPriorities;
use App\Models\Scopes\UserRoleScope;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;
use PrinsFrank\Standards\Country\CountryAlpha3;
use PrinsFrank\Standards\Language\LanguageAlpha2;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

#[ScopedBy([UserRoleScope::class])]
class Partner extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'internal_referent_id',
        'area_manager_id',

        'company',
        'email',
        'country',
        'payment_term_id',
        'commercial_category',
        'priority',
        'minimum_orderable',
        'handling_and_packing',
        'delivery_terms',
        'countries_of_expertise',
        'notes',
    ];

    protected $casts = [
        'type' => PartnerTypes::class,
        'commercial_category' => CommercialCategories::class,
        'delivery_terms' => DeliveryTerms::class,
        'priority' => ClientPartnerPriorities::class,
        'minimum_orderable' => 'integer',
        'handling_and_packing' => 'integer',
        'country' => CountryAlpha3::class,
        'countries_of_expertise' => 'json',
    ];

    /**
     * Get the minimum orderable amount.
     *
     * @return float
     */
    public function getMinimumOrderableAttribute(): ?float
    {
        return $this->attributes['minimum_orderable'] ? $this->attributes['minimum_orderable'] / 100 : NULL;
    }

    /**
     * Set the minimum orderable amount.
     *
     * @param float $value
     * @return void
     */
    public function setMinimumOrderableAttribute(?float $value): void
    {
        $this->attributes['minimum_orderable'] = $value === null ? null : (int) ($value * 100);
    }

    /**
     * Get the internal referent for the client.
     */
    public function internalReferent(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the area manager for the partner.
     */
    public function areaManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'area_manager_id');
    }

    /**
     * Get all the users for the partner.
     */
    public function users(): MorphToMany
    {
        return $this->morphToMany(User::class, 'userable');
    }

    /**
     * Get all the contacts for the partner.
     */
    public function contacts(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class);
    }

    /**
     * Get all the brands that belong to the partner.
     */
    public function brands(): BelongsToMany
    {
        return $this->belongsToMany(Brand::class);
    }

    /**
     * Get all the addresses for the partner.
     */
    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'addressable');
    }

    /**
     * Get the clients for the partner.
     */
    public function clients(): HasMany
    {
        return $this->hasMany(Client::class);
    }

    /**
     * Get the discount groups for the partner.
     */
    public function discountGroups(): BelongsToMany
    {
        return $this->belongsToMany(DiscountGroup::class)
            ->withPivot('discount');
    }

    /**
     * Get the payment term for the partner.
     */
    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    /**
     * Get the orders for the partner.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the projects for the partner.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get the country label name from alpha3 code.
     */
    public function getCountryLabelAttribute($countryAlpha3): string
    {
        if ($countryAlpha3 === null) {
            return '-';
        }
        else {
            return CountryAlpha3::from($countryAlpha3)->getNameInLanguage(LanguageAlpha2::English);
        }
    }

    public function owners(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->withPivot('partner_id');
    }
}
