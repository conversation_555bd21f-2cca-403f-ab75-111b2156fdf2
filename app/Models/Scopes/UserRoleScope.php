<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class UserRoleScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        if (auth()->user()?->isAdmin()) {
            return;
        }

        $modelName = class_basename($model);
        $pivotTable = strtolower($modelName) . '_user';
        $builder->whereIn($model->getTable() . '.id', function ($query) use ($pivotTable, $modelName) {
            $query->select(strtolower($modelName). '_id')
                ->from($pivotTable)
                ->where('user_id', auth()->id());
        });
    }
}
