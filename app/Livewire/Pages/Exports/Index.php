<?php

namespace App\Livewire\Pages\Exports;

use App\Models\Product;
use Livewire\Component;
use App\Models\CustomProduct;
use Spatie\SimpleExcel\SimpleExcelWriter;

class Index extends Component
{
    public function render()
    {
        return view('livewire.pages.exports.index');
    }

    public function downloadCustomProducts(): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $products = CustomProduct::all();
        $fileName = 'custom-products.xlsx';

        $writer = SimpleExcelWriter::streamDownload($fileName);

        foreach ($products as $index => $product) {
            $writer->addRow([
                'CREATED BY' => $product->user?->email ?? '',
                'TEMPORARY SKUS' => $product->id,
                'SKU' => $product->sku,
                'ADHOC SKU' => '',
                'BRAND NAME' => $product->brand_name,
                'BRAND PREFIX' => '',
                'COLLECTION CODE' => '',
                'COLLECTION NAME' => '',
                'SUPPLIER COLOR' => $product->supplier_color,
                'COLOR' => '',
                'DESCRIPTION BRG' => $product->description,
                'DESTINATION ROOM' => '',
                'DIMENSION' => $product->dimensions,
                'DISCOUNT GROUP' => '',
                'ITEM STATUS' => '',
                'LEAD TIME' => '',
                'MATERIAL' => '',
                'PRODUCT TYPE' => '',
                'PURCHASE UNIT' => '',
                'SELLING PRICE' => '',
            ]);

            // Flush if over 1000 loop iterations
            if ($index % 1000 === 0) {
                flush();
            }
        }

        return response()->streamDownload(function () use ($writer) {
            $writer->close();
        }, $fileName);
    }

    public function downloadMissingDiscountGroup(): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $products = Product::whereDoesntHave('discountGroup')->get();
        $fileName = 'missing-discount-group-products.xlsx';

        $writer = SimpleExcelWriter::streamDownload($fileName);

        foreach ($products as $product) {
            $writer->addRow([
                'id' => $product->id,
                'sku' => $product->sku,
            ]);
        }

        return response()->streamDownload(function () use ($writer) {
            $writer->close();
        }, $fileName);
    }

    public function downloadProductsWithoutImageUsedInOrders(): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $products = Product::whereHas('orderRows', function ($query) {
            $query->where('product_id', '!=', null);
        })->whereDoesntHave('image')->get();
        $fileName = 'products-without-image-used-in-orders.xlsx';

        $writer = SimpleExcelWriter::streamDownload($fileName);

        foreach ($products as $product) {
            $writer->addRow([
                'id' => $product->id,
                'sku' => $product->sku,
            ]);
        }

        return response()->streamDownload(function () use ($writer) {
            $writer->close();
        }, $fileName);
    }
}
