<?php

namespace App\Jobs\Import\Products;

use App\Models\{Brand, Collection, DiscountGroup, Product, Module, Option, Price};
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Bus\Queueable;
use App\Jobs\Import\Concerns\LogsImportProgress;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ImportModularProductJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsImportProgress;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * The product row data.
     *
     * @var array
     */
    public $row;

    /**
     * The import ID for logging purposes.
     *
     * @var int|null
     */
    public ?int $importId;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row,
        public ?int $importId = null
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $row = collect($this->row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();

            $brand = Brand::where('prefix', $row['brand_prefix'])->first();

            $collection = (!empty($row['collection_code']) && !empty($row['collection_name']))
                ? Collection::firstOrCreate(['code' => $row['collection_code']], ['name' => $row['collection_name']])
                : null;

            $discountGroup = !empty($row['discount_group_code'])
                ? DiscountGroup::where('code', $row['discount_group_code'])->first()
                : null;

            // Check if the product is soft deleted and if so, restore it
            if (Product::onlyTrashed()->where('sku', $row['sku'])->exists()) {
                Product::onlyTrashed()->where('sku', $row['sku'])->restore();
            }

            Product::upsert([
                'template' => $row['template'] ?? null,
                'brand_id' => $brand->id ?? null,
                'collection_id' => $collection->id ?? null,
                'discount_group_id' => $discountGroup->id ?? null,
                'sku' => $row['sku'],
                'adhoc_sku' => $row['adhoc_sku'] ?? null,
                'adhoc_discount_category' => $row['adhoc_discount_category'] ?? null,
                'ean_code' => $row['ean_code'] ?? null,
                'description' => $row['description'],
                'extra_description' => $row['extra_description'] ?? null,
                'dimensions' => $row['dimensions'] ?? null,
                'selling_price' => $row['selling_price'] ? ($row['selling_price'] * 100) : null,
                'starting_selling_price' => $row['starting_selling_price'] ? ($row['starting_selling_price'] * 100) : null,
                'purchasing_price' => $row['purchasing_price'] ? ($row['purchasing_price'] * 100) : null,
                'purchase_units' => $row['purchase_units'] ?? null,
                'leadtime' => $row['leadtime'] ?? null,
                'type' => $row['type'] ?? null,
                'country_of_origin' => $row['country_of_origin'] ?? null,
                'supplier_color' => $row['supplier_color'] ?? null,
                'hs_code' => $row['hs_code'] ?? null,
                'height' => $row['height'] ?? null,
                'length' => $row['length'] ?? null,
                'width' => $row['width'] ?? null,
                'diameter' => $row['diameter'] ?? null,
                'volume' => $row['volume'] ?? null,
                'capacity' => $row['capacity'] ?? null,
                'net_weight' => $row['net_weight'] ?? null,
            ], ['sku']);

            $product = Product::where('sku', $row['sku'])->first();

            if (!empty($row['image'])) {
                try {
                    $response = Http::timeout(5)->get($row['image']);
                    if ($response->successful()) {
                        $extension = pathinfo(parse_url($row['image'], PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';
                        $fileName = 'products/' . $row['sku'] . '.' . $extension;
                        Storage::disk(config('filesystems.public'))->put($fileName, $response->body());
                        $product->image = $fileName;
                        $product->save();
                    }
                } catch (\Exception $e) {
                    Log::warning("Failed to download image for modular product", [
                        'sku' => $row['sku'],
                        'image_url' => $row['image'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $this->attachTags($product, $row, 'destination_room');
            $this->attachTags($product, $row, 'style');
            $this->attachTags($product, $row, 'material');
            $this->attachTags($product, $row, 'color');
            $this->attachTags($product, $row, 'general_feature');

            $this->updateOrCreateOptions($row, $product);
            $this->updatePrices($row, $product);

            Log::info("Modular product imported successfully", ['sku' => $row['sku']]);

        } catch (\Exception $e) {
            Log::error("Failed to import modular product", [
                'sku' => $this->row['sku'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    protected function attachTags(Product $product, array $row, string $key): void
    {
        if (!empty($row[$key])) {
            $tags = array_map('trim', explode(',', $row[$key]));
            $product->attachTags($tags, $key);
        }
    }

    protected function updateOrCreateOptions(array $row, Product $product): void
    {
        $product->modules()->forceDelete();
        $modules = [];

        for ($i = 1; $i <= 10; $i++) {
            if (!empty($row["relation_$i"])) {
                $modules[$i] = array_map('trim', explode(',', $row["relation_$i"]));
            }
        }

        $mergedModules = [];

        if (!empty($row['relationship_join'])) {
            foreach (explode(';', $row['relationship_join']) as $join) {
                $mergedSku = [];
                foreach (explode(',', $join) as $m) {
                    $key = (int) trim($m);
                    if (isset($modules[$key])) {
                        $mergedSku = array_merge($mergedSku, $modules[$key]);
                        unset($modules[$key]);
                    }
                }
                if ($mergedSku) $mergedModules[] = array_unique($mergedSku);
            }
            $mergedModules = array_merge($mergedModules, array_values($modules));
        } else {
            $mergedModules = array_values($modules);
        }

        foreach ($mergedModules as $index => $skuGroup) {
            $module = Module::create([
                'name' => 'Module ' . ($index + 1),
                'product_id' => $product->id,
            ]);

            $optionIds = Option::whereIn('sku', $skuGroup)->pluck('id')->toArray();
            $module->options()->sync($optionIds);
        }
    }

    protected function updatePrices(array $row, Product $product): void
    {
        foreach (['selling', 'purchasing'] as $type) {
            $key = $type . '_prices';
            if (!empty($row[$key])) {
                $prices = explode(';', $row[$key]);
                Price::where('product_id', $product->id)->where('type', $type)->delete();
                $data = [];
                foreach ($prices as $i => $price) {
                    $value = (int) round(floatval($price) * 100);
                    if ($value > 0) {
                        $data[] = [
                            'type' => $type,
                            'name' => $i + 1,
                            'price' => $value,
                            'product_id' => $product->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }
                }
                Price::insert($data);
            }
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        // Log the failure for import tracking
        $this->logFailure($exception);

        Log::error("ImportModularProductJob failed after all retries", [
            'sku' => $this->row['sku'] ?? 'unknown',
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
