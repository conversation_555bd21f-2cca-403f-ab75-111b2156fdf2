<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

abstract class BasePolicy
{
    protected string $resource = '';

    /**
     * Map model names to resource names for permissions.
     */
    protected static array $permissionMap = [
        'User' => 'users',
        'Role' => 'roles',
        'Order' => 'orders',
        'Project' => 'projects',
        'Client' => 'clients',
        'Partner' => 'partners',
        'Brand' => 'brands',
        'Product' => 'products',
        'DiscountGroup' => 'discount_groups',
        'Supplier' => 'suppliers',
        'Collection' => 'collections',
        'Contact' => 'contacts',
    ];

    /* List of Models with pivot table */
    protected static array $pivotModels = [
        'Contact',
        'Partner',
        'Order',
        'Project',
        'Client',
    ];

    protected function hasPivotRelation(User $user, Model $model): bool
    {
        $modelName = class_basename($model);
        if (!in_array($modelName, static::$pivotModels)) {
            return true;
        }

        if (method_exists($model, 'owners')) {
            return $model->owners()->where('users.id', $user->id)->exists();
        }

        return false;
    }

    /**
     * Retrieve the resource name for the policy from model name.
     */
    protected function getResourceName(): string
    {
        if ($this->resource) {
            return $this->resource;
        }

        $modelName = class_basename(static::getModelClass());
        return static::$permissionMap[$modelName] ?? '';
    }

    /**
     * Retrive the model class name from the policy class.
     */
    protected static function getModelClass(): string
    {
        return str_replace('Policy', '', static::class);
    }

    public function before(User $user, $ability)
    {
        // if user has no roles, allow all actions
        if ($user->roles->isEmpty()) {
            return true;
        }

        // if user is super admin, allow all actions
        if ($user->isAdmin()) {
            return true;
        }
    }

    public function viewAny(User $user): bool
    {
        return $user->can("read_" . $this->getResourceName());
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Model $model): bool
    {
        // check if user has pivot relation
        if (!$this->hasPivotRelation($user, $model)) {
            return false;
        }

        return $user->can("read_" . $this->getResourceName());
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can("write_" . $this->getResourceName());
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Model $model): bool
    {
        // check if user has pivot relation
        if (!$this->hasPivotRelation($user, $model)) {
            return false;
        }

        return $user->can("write_" . $this->getResourceName());
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Model $model): bool
    {
        // check if user has pivot relation
        if (!$this->hasPivotRelation($user, $model)) {
            return false;
        }

        return $user->can("write_" . $this->getResourceName());
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Model $model): bool
    {
        // check if user has pivot relation
        if (!$this->hasPivotRelation($user, $model)) {
            return false;
        }

        return $user->can("write_" . $this->getResourceName());
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Model $model): bool
    {
        // check if user has pivot relation
        if (!$this->hasPivotRelation($user, $model)) {
            return false;
        }

        return $user->can("write_" . $this->getResourceName());
    }
}
